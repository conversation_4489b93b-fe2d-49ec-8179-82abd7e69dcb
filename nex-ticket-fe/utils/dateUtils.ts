import { format } from 'date-fns'

export function formatDateToDate(date: Date): string {
  return format(date, 'EEE, dd MMM yyyy')
}

export function formatDateToTime(date: Date): string {
  return format(date, 'HH:mm')
}

export function toLocalISOString(date: Date): string {
  const offsetMs = date.getTimezoneOffset() * 60 * 1000
  const localTime = new Date(date.getTime() - offsetMs)
  return localTime.toISOString()
}

/**
 * Safely converts a date (Date object or string) to local ISO string format
 * Returns empty string if date is invalid or null/undefined
 */
export function safeToLocalISOString(date: Date | string | null | undefined): string {
  if (!date) {
    return ''
  }

  try {
    // If it's already a string, try to parse it first
    const dateObj = typeof date === 'string' ? new Date(date) : date

    // Check if the date is valid
    if (Number.isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to safeToLocalISOString:', date)
      return ''
    }

    return toLocalISOString(dateObj)
  }
  catch (error) {
    console.error('Error converting date to local ISO string:', error, date)
    return ''
  }
}
